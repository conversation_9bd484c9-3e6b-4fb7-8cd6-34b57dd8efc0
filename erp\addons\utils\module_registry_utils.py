"""
Module registry utilities for addon installation

This module provides shared utility functions for managing addon state in the
ir.module.module table. These functions support the XML-based module registration
approach where module records are loaded via XML data files.
"""

from datetime import datetime
from typing import Any, Dict, Optional

from ...logging import get_logger

logger = get_logger(__name__)


async def unregister_addon_from_module_table(db_manager, addon_name: str) -> bool:
    """
    Mark an addon as uninstalled in the ir.module.module table

    Args:
        db_manager: Database manager instance
        addon_name: Technical name of the addon

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        current_time = datetime.now()
        await db_manager.execute(
            """UPDATE ir_module_module SET
               state = $1, update_at = $2, action_at = $3
               WHERE name = $4""",
            "uninstalled",
            current_time,
            current_time,
            addon_name,
        )

        logger.info(
            f"✓ Addon '{addon_name}' marked as uninstalled in ir.module.module table"
        )
        return True

    except Exception as e:
        logger.error(
            f"Failed to unregister addon '{addon_name}' from ir.module.module: {e}"
        )
        return False


async def update_addon_state_in_module_table(
    db_manager, addon_name: str, state: str
) -> bool:
    """
    Update addon state in the ir.module.module table

    Args:
        db_manager: Database manager instance
        addon_name: Technical name of the addon
        state: New state ('installed', 'uninstalled', 'to_upgrade', etc.)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        current_time = datetime.now()
        await db_manager.execute(
            """UPDATE ir_module_module SET
               state = $1, update_at = $2, action_at = $3
               WHERE name = $4""",
            state,
            current_time,
            current_time,
            addon_name,
        )

        logger.debug(
            f"✓ Addon '{addon_name}' state updated to '{state}' in ir.module.module table"
        )
        return True

    except Exception as e:
        logger.error(f"Failed to update addon '{addon_name}' state to '{state}': {e}")
        return False


def read_manifest_file(manifest_path: str) -> Optional[Dict[str, Any]]:
    """
    Read and parse an addon manifest file

    Args:
        manifest_path: Path to the __manifest__.py file

    Returns:
        Dictionary containing manifest data, or None if failed
    """
    try:
        import ast

        with open(manifest_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Parse the manifest as Python code
        manifest_data = ast.literal_eval(content)

        if not isinstance(manifest_data, dict):
            logger.error(f"Manifest file {manifest_path} does not contain a dictionary")
            return None

        return manifest_data

    except Exception as e:
        logger.error(f"Failed to read manifest from {manifest_path}: {e}")
        return None



